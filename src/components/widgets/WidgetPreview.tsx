import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Laptop,
  Smartphone,
  Tablet,
  Send,
  X,
  Minimize2,
  Maximize2,
} from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: Date;
}

interface WidgetPreviewProps {
  primaryColor?: string;
  secondaryColor?: string;
  headerText?: string;
  welcomeMessage?: string;
  botName?: string;
  botAvatarUrl?: string;
  companyLogoUrl?: string;
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  width?: number;
  height?: number;
}

const WidgetPreview = ({
  primaryColor = "#F472B6",
  secondaryColor = "#ffffff",
  headerText = "Chat Support",
  welcomeMessage = "Hello! How can I help you today?",
  botName = "AI Assistant",
  botAvatarUrl = "https://api.dicebear.com/7.x/avataaars/svg?seed=assistant",
  companyLogoUrl = "https://api.dicebear.com/7.x/initials/svg?seed=CS",
  position = "bottom-right",
  width = 350,
  height = 500,
}: WidgetPreviewProps) => {
  const [deviceView, setDeviceView] = useState<"desktop" | "tablet" | "mobile">(
    "desktop",
  );
  const [isOpen, setIsOpen] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: welcomeMessage,
      sender: "bot",
      timestamp: new Date(),
    },
  ]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages([...messages, userMessage]);
    setInputValue("");

    // Simulate bot response after a short delay
    setTimeout(() => {
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          "Thank you for your message. I'm a placeholder response from the AI assistant.",
        sender: "bot",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, botMessage]);
    }, 1000);
  };

  const getDeviceWidth = () => {
    switch (deviceView) {
      case "desktop":
        return width;
      case "tablet":
        return Math.min(width, 320);
      case "mobile":
        return Math.min(width, 280);
      default:
        return width;
    }
  };

  const getDeviceHeight = () => {
    switch (deviceView) {
      case "desktop":
        return height;
      case "tablet":
        return Math.min(height, 450);
      case "mobile":
        return Math.min(height, 400);
      default:
        return height;
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case "bottom-right":
        return "bottom-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "top-right":
        return "top-4 right-4";
      case "top-left":
        return "top-4 left-4";
      default:
        return "bottom-4 right-4";
    }
  };

  return (
    <div className="bg-gray-100 p-8 rounded-lg w-full flex flex-col items-center">
      <div className="mb-6 flex items-center justify-center space-x-4">
        <Button
          variant={deviceView === "desktop" ? "default" : "outline"}
          size="sm"
          onClick={() => setDeviceView("desktop")}
        >
          <Laptop className="h-4 w-4 mr-2" />
          Desktop
        </Button>
        <Button
          variant={deviceView === "tablet" ? "default" : "outline"}
          size="sm"
          onClick={() => setDeviceView("tablet")}
        >
          <Tablet className="h-4 w-4 mr-2" />
          Tablet
        </Button>
        <Button
          variant={deviceView === "mobile" ? "default" : "outline"}
          size="sm"
          onClick={() => setDeviceView("mobile")}
        >
          <Smartphone className="h-4 w-4 mr-2" />
          Mobile
        </Button>
      </div>

      <div
        className="relative border border-gray-200 rounded-lg bg-white shadow-lg"
        style={{ width: "100%", height: "600px", maxWidth: "800px" }}
      >
        <div className="p-4 h-full flex flex-col">
          <div className="flex-1 relative">
            {/* Browser mockup */}
            <div className="w-full h-full bg-gray-50 rounded-lg border border-gray-200 overflow-hidden flex flex-col">
              <div className="bg-gray-200 p-2 flex items-center">
                <div className="flex space-x-1.5">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <div className="mx-auto bg-white rounded-md px-2 py-1 text-xs text-gray-500">
                  example.com
                </div>
              </div>
              <div className="flex-1 p-4 relative">
                {/* Website content mockup */}
                <div className="w-full h-full flex flex-col">
                  <div className="h-12 bg-gray-100 mb-4 rounded-md"></div>
                  <div className="flex-1 flex gap-4">
                    <div className="w-2/3 flex flex-col gap-3">
                      <div className="h-40 bg-gray-100 rounded-md"></div>
                      <div className="h-20 bg-gray-100 rounded-md"></div>
                      <div className="h-60 bg-gray-100 rounded-md"></div>
                    </div>
                    <div className="w-1/3 flex flex-col gap-3">
                      <div className="h-24 bg-gray-100 rounded-md"></div>
                      <div className="h-32 bg-gray-100 rounded-md"></div>
                    </div>
                  </div>
                </div>

                {/* Chat widget */}
                <div className={`absolute ${getPositionClasses()}`}>
                  {!isOpen ? (
                    <Button
                      className="rounded-full shadow-lg"
                      style={{ backgroundColor: primaryColor }}
                      size="icon"
                      onClick={() => setIsOpen(true)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-message-circle"
                      >
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                      </svg>
                    </Button>
                  ) : (
                    <Card
                      className="overflow-hidden shadow-lg flex flex-col"
                      style={{
                        width: `${getDeviceWidth()}px`,
                        height: isMinimized ? "auto" : `${getDeviceHeight()}px`,
                        backgroundColor: secondaryColor,
                      }}
                    >
                      <div
                        className="p-3 flex justify-between items-center"
                        style={{ backgroundColor: primaryColor, color: "#fff" }}
                      >
                        <div className="flex items-center">
                          <Avatar className="h-8 w-8 mr-2">
                            <AvatarImage
                              src={companyLogoUrl}
                              alt="Company Logo"
                            />
                            <AvatarFallback>CL</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{headerText}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          {isMinimized ? (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-white"
                              onClick={() => setIsMinimized(false)}
                            >
                              <Maximize2 className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-white"
                              onClick={() => setIsMinimized(true)}
                            >
                              <Minimize2 className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-white"
                            onClick={() => setIsOpen(false)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {!isMinimized && (
                        <>
                          <CardContent className="flex-1 p-3 overflow-y-auto flex flex-col gap-3">
                            {messages.map((message) => (
                              <div
                                key={message.id}
                                className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                              >
                                <div
                                  className={`max-w-[80%] rounded-lg p-3 ${
                                    message.sender === "user"
                                      ? `bg-blue-500 text-white`
                                      : "bg-gray-100 text-gray-800"
                                  }`}
                                >
                                  {message.sender === "bot" && (
                                    <div className="flex items-center mb-1">
                                      <Avatar className="h-6 w-6 mr-2">
                                        <AvatarImage
                                          src={botAvatarUrl}
                                          alt="Bot Avatar"
                                        />
                                        <AvatarFallback>AI</AvatarFallback>
                                      </Avatar>
                                      <span className="text-xs font-medium">
                                        {botName}
                                      </span>
                                    </div>
                                  )}
                                  <p className="text-sm">{message.content}</p>
                                  <div className="text-xs opacity-70 mt-1 text-right">
                                    {message.timestamp.toLocaleTimeString([], {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </CardContent>

                          <div className="p-3 border-t">
                            <div className="flex items-center">
                              <Input
                                placeholder="Type your message..."
                                value={inputValue}
                                onChange={(e) => setInputValue(e.target.value)}
                                onKeyPress={(e) =>
                                  e.key === "Enter" && handleSendMessage()
                                }
                                className="flex-1"
                              />
                              <Button
                                className="ml-2"
                                size="icon"
                                onClick={handleSendMessage}
                                style={{ backgroundColor: primaryColor }}
                              >
                                <Send className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="mt-2 text-center">
                              <span className="text-xs text-gray-500">
                                Powered by AI Support
                              </span>
                            </div>
                          </div>
                        </>
                      )}
                    </Card>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 w-full max-w-md">
        <h3 className="text-lg font-medium mb-2">Widget Position</h3>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant={position === "bottom-right" ? "default" : "outline"}
            onClick={() => position !== "bottom-right" && setIsOpen(true)}
            className="justify-start"
          >
            Bottom Right
          </Button>
          <Button
            variant={position === "bottom-left" ? "default" : "outline"}
            onClick={() => position !== "bottom-left" && setIsOpen(true)}
            className="justify-start"
          >
            Bottom Left
          </Button>
          <Button
            variant={position === "top-right" ? "default" : "outline"}
            onClick={() => position !== "top-right" && setIsOpen(true)}
            className="justify-start"
          >
            Top Right
          </Button>
          <Button
            variant={position === "top-left" ? "default" : "outline"}
            onClick={() => position !== "top-left" && setIsOpen(true)}
            className="justify-start"
          >
            Top Left
          </Button>
        </div>
      </div>
    </div>
  );
};

export default WidgetPreview;
