import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Plus, TrendingUp, TrendingDown, Activity } from "lucide-react";
import ProjectCard from "@/components/dashboard/ProjectCard";
import AiProviderCard from "@/components/ai-providers/AiProviderCard";
import { useApi } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { Project, AiProvider } from "@/types";
import { useNavigate } from "react-router-dom";

const HomePage = () => {
  const navigate = useNavigate();

  const {
    data: projects,
    loading: projectsLoading,
    error: projectsError,
  } = useApi<Project[]>(() => apiService.getProjects().then((res) => res), []);

  const {
    data: aiProviders,
    loading: providersLoading,
    error: providersError,
  } = useApi<AiProvider[]>(
    () => apiService.getAiProviders().then((res) => res),
    [],
  );

  const handleProjectEdit = (id: string) => {
    navigate(`/projects/${id}/edit`);
  };

  const handleProjectView = (id: string) => {
    navigate(`/projects/${id}`);
  };

  const handleProjectDelete = (id: string) => {
    // Implement delete functionality
    console.log("Delete project:", id);
  };

  // Calculate overview stats
  const totalActiveChats =
    projects?.reduce((sum, project) => sum + project.metrics.activeChats, 0) ||
    0;
  const averageResponseTime =
    projects?.reduce(
      (sum, project) => sum + project.metrics.averageResponseTime,
      0,
    ) / (projects?.length || 1) || 0;
  const averageSatisfaction =
    projects?.reduce(
      (sum, project) => sum + project.metrics.userSatisfaction,
      0,
    ) / (projects?.length || 1) || 0;

  const connectedProviders =
    aiProviders?.filter((p) => p.connectionStatus === "connected").length || 0;

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's an overview of your AI support system.
          </p>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Chats</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalActiveChats}</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +12% from last hour
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {averageResponseTime.toFixed(1)}s
            </div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingDown className="h-3 w-3 mr-1 text-green-500" />
              -0.3s from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              User Satisfaction
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(averageSatisfaction * 20).toFixed(0)}%
            </div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +2.1% from last week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Connected Providers
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectedProviders}</div>
            <p className="text-xs text-muted-foreground">
              of {aiProviders?.length || 0} total providers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Projects and AI Providers */}
      <Tabs defaultValue="projects" className="space-y-6">
        <TabsList>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="ai-providers">AI Providers</TabsTrigger>
        </TabsList>

        {/* Projects Tab */}
        <TabsContent value="projects" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">
                Your Projects
              </h2>
              <p className="text-muted-foreground">
                Manage your AI-powered chat widgets and their configurations.
              </p>
            </div>
            <Button onClick={() => navigate("/projects/new")}>
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Button>
          </div>

          {projectsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : projectsError ? (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Error loading projects: {projectsError}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects?.map((project) => (
                <ProjectCard
                  key={project.id}
                  id={project.id}
                  name={project.name}
                  description={project.description}
                  status={project.status}
                  metrics={project.metrics}
                  onEdit={handleProjectEdit}
                  onView={handleProjectView}
                  onDelete={handleProjectDelete}
                />
              ))}
            </div>
          )}
        </TabsContent>

        {/* AI Providers Tab */}
        <TabsContent value="ai-providers" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold tracking-tight">
                AI Providers
              </h2>
              <p className="text-muted-foreground">
                Configure and manage your AI provider connections and API keys.
              </p>
            </div>
            <Button onClick={() => navigate("/ai-providers")}>
              <Plus className="mr-2 h-4 w-4" />
              Add Provider
            </Button>
          </div>

          {providersLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : providersError ? (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Error loading AI providers: {providersError}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {aiProviders?.map((provider) => (
                <AiProviderCard key={provider.id} provider={provider} />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HomePage;
