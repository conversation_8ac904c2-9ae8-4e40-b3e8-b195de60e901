import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import WidgetPreview from "@/components/widgets/WidgetPreview";
import { useApi, useAsyncAction } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { Project, WidgetConfig } from "@/types";
import { Pa<PERSON>, Settings, Code, Save } from "lucide-react";

const WidgetCustomizationPage = () => {
  const { toast } = useToast();
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [widgetConfig, setWidgetConfig] = useState<WidgetConfig>({
    id: "1",
    primaryColor: "#F472B6",
    secondaryColor: "#ffffff",
    headerText: "Chat Support",
    welcomeMessage: "Hello! How can I help you today?",
    botName: "AI Assistant",
    botAvatarUrl: "https://api.dicebear.com/7.x/avataaars/svg?seed=assistant",
    companyLogoUrl: "https://api.dicebear.com/7.x/initials/svg?seed=CS",
    position: "bottom-right",
    width: 350,
    height: 500,
    isEnabled: true,
    allowFileUpload: true,
    showTypingIndicator: true,
    enableSoundNotifications: false,
  });

  const { data: projects, loading: projectsLoading } = useApi<Project[]>(
    () => apiService.getProjects(),
    [],
  );

  const { execute: saveConfig, loading: saving } = useAsyncAction(
    (config: Partial<WidgetConfig>) =>
      apiService.updateWidgetConfig(selectedProject, config),
  );

  const handleSaveConfig = async () => {
    if (!selectedProject) {
      toast({
        title: "Error",
        description: "Please select a project first",
        variant: "destructive",
      });
      return;
    }

    try {
      await saveConfig(widgetConfig);
      toast({
        title: "Success",
        description: "Widget configuration saved successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save widget configuration",
        variant: "destructive",
      });
    }
  };

  const generateEmbedCode = () => {
    return `<!-- AI Support Widget -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://your-domain.com/widget.js';
    script.setAttribute('data-project-id', '${selectedProject}');
    script.setAttribute('data-primary-color', '${widgetConfig.primaryColor}');
    script.setAttribute('data-position', '${widgetConfig.position}');
    document.head.appendChild(script);
  })();
</script>`;
  };

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Widget Customization
          </h1>
          <p className="text-muted-foreground">
            Customize the appearance and behavior of your chat widgets.
          </p>
        </div>
        <Button
          onClick={handleSaveConfig}
          disabled={saving || !selectedProject}
        >
          <Save className="mr-2 h-4 w-4" />
          {saving ? "Saving..." : "Save Changes"}
        </Button>
      </div>

      {/* Project Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Project</CardTitle>
          <CardDescription>
            Choose which project's widget you want to customize.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a project" />
            </SelectTrigger>
            <SelectContent>
              {projects?.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedProject && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <div className="space-y-6">
            <Tabs defaultValue="appearance" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="appearance">
                  <Palette className="mr-2 h-4 w-4" />
                  Appearance
                </TabsTrigger>
                <TabsTrigger value="behavior">
                  <Settings className="mr-2 h-4 w-4" />
                  Behavior
                </TabsTrigger>
                <TabsTrigger value="embed">
                  <Code className="mr-2 h-4 w-4" />
                  Embed
                </TabsTrigger>
              </TabsList>

              {/* Appearance Tab */}
              <TabsContent value="appearance" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Colors & Branding</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="primary-color">Primary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="primary-color"
                            type="color"
                            value={widgetConfig.primaryColor}
                            onChange={(e) =>
                              setWidgetConfig({
                                ...widgetConfig,
                                primaryColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border rounded"
                          />
                          <Input
                            value={widgetConfig.primaryColor}
                            onChange={(e) =>
                              setWidgetConfig({
                                ...widgetConfig,
                                primaryColor: e.target.value,
                              })
                            }
                            placeholder="#F472B6"
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="secondary-color">Secondary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="secondary-color"
                            type="color"
                            value={widgetConfig.secondaryColor}
                            onChange={(e) =>
                              setWidgetConfig({
                                ...widgetConfig,
                                secondaryColor: e.target.value,
                              })
                            }
                            className="w-16 h-10 p-1 border rounded"
                          />
                          <Input
                            value={widgetConfig.secondaryColor}
                            onChange={(e) =>
                              setWidgetConfig({
                                ...widgetConfig,
                                secondaryColor: e.target.value,
                              })
                            }
                            placeholder="#ffffff"
                          />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="header-text">Header Text</Label>
                        <Input
                          id="header-text"
                          value={widgetConfig.headerText}
                          onChange={(e) =>
                            setWidgetConfig({
                              ...widgetConfig,
                              headerText: e.target.value,
                            })
                          }
                          placeholder="Chat Support"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="welcome-message">Welcome Message</Label>
                        <Textarea
                          id="welcome-message"
                          value={widgetConfig.welcomeMessage}
                          onChange={(e) =>
                            setWidgetConfig({
                              ...widgetConfig,
                              welcomeMessage: e.target.value,
                            })
                          }
                          placeholder="Hello! How can I help you today?"
                          rows={3}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bot-name">Bot Name</Label>
                        <Input
                          id="bot-name"
                          value={widgetConfig.botName}
                          onChange={(e) =>
                            setWidgetConfig({
                              ...widgetConfig,
                              botName: e.target.value,
                            })
                          }
                          placeholder="AI Assistant"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Position & Size</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="position">Widget Position</Label>
                      <Select
                        value={widgetConfig.position}
                        onValueChange={(value: any) =>
                          setWidgetConfig({ ...widgetConfig, position: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bottom-right">
                            Bottom Right
                          </SelectItem>
                          <SelectItem value="bottom-left">
                            Bottom Left
                          </SelectItem>
                          <SelectItem value="top-right">Top Right</SelectItem>
                          <SelectItem value="top-left">Top Left</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="width">Width (px)</Label>
                        <Input
                          id="width"
                          type="number"
                          value={widgetConfig.width}
                          onChange={(e) =>
                            setWidgetConfig({
                              ...widgetConfig,
                              width: parseInt(e.target.value),
                            })
                          }
                          min={300}
                          max={500}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="height">Height (px)</Label>
                        <Input
                          id="height"
                          type="number"
                          value={widgetConfig.height}
                          onChange={(e) =>
                            setWidgetConfig({
                              ...widgetConfig,
                              height: parseInt(e.target.value),
                            })
                          }
                          min={400}
                          max={700}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Behavior Tab */}
              <TabsContent value="behavior" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Widget Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Enable Widget</Label>
                        <p className="text-sm text-muted-foreground">
                          Show the widget on your website
                        </p>
                      </div>
                      <Switch
                        checked={widgetConfig.isEnabled}
                        onCheckedChange={(checked) =>
                          setWidgetConfig({
                            ...widgetConfig,
                            isEnabled: checked,
                          })
                        }
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>File Upload</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow users to upload files
                        </p>
                      </div>
                      <Switch
                        checked={widgetConfig.allowFileUpload}
                        onCheckedChange={(checked) =>
                          setWidgetConfig({
                            ...widgetConfig,
                            allowFileUpload: checked,
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Typing Indicator</Label>
                        <p className="text-sm text-muted-foreground">
                          Show when the bot is typing
                        </p>
                      </div>
                      <Switch
                        checked={widgetConfig.showTypingIndicator}
                        onCheckedChange={(checked) =>
                          setWidgetConfig({
                            ...widgetConfig,
                            showTypingIndicator: checked,
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Sound Notifications</Label>
                        <p className="text-sm text-muted-foreground">
                          Play sound for new messages
                        </p>
                      </div>
                      <Switch
                        checked={widgetConfig.enableSoundNotifications}
                        onCheckedChange={(checked) =>
                          setWidgetConfig({
                            ...widgetConfig,
                            enableSoundNotifications: checked,
                          })
                        }
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Embed Tab */}
              <TabsContent value="embed" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Embed Code</CardTitle>
                    <CardDescription>
                      Copy this code and paste it into your website's HTML.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="bg-muted p-4 rounded-lg">
                        <pre className="text-sm overflow-x-auto">
                          <code>{generateEmbedCode()}</code>
                        </pre>
                      </div>
                      <Button
                        onClick={() => {
                          navigator.clipboard.writeText(generateEmbedCode());
                          toast({
                            title: "Copied!",
                            description: "Embed code copied to clipboard",
                          });
                        }}
                      >
                        Copy Code
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Live Preview</CardTitle>
                <CardDescription>
                  See how your widget will look on your website.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <WidgetPreview
                  primaryColor={widgetConfig.primaryColor}
                  secondaryColor={widgetConfig.secondaryColor}
                  headerText={widgetConfig.headerText}
                  welcomeMessage={widgetConfig.welcomeMessage}
                  botName={widgetConfig.botName}
                  botAvatarUrl={widgetConfig.botAvatarUrl}
                  companyLogoUrl={widgetConfig.companyLogoUrl}
                  position={widgetConfig.position}
                  width={widgetConfig.width}
                  height={widgetConfig.height}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {!selectedProject && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <Settings className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  Select a project to customize
                </h3>
                <p className="text-muted-foreground">
                  Choose a project from the dropdown above to start customizing
                  its widget.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default WidgetCustomizationPage;
