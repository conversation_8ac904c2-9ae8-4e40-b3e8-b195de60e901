import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useApi, useAsyncAction } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { Project, Document } from "@/types";
import {
  Upload,
  FileText,
  Globe,
  Trash2,
  Download,
  RefreshCw,
  Plus,
  Search,
  Filter,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";

const KnowledgeBasePage = () => {
  const { toast } = useToast();
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [urlDialogOpen, setUrlDialogOpen] = useState(false);
  const [newUrl, setNewUrl] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { data: projects, loading: projectsLoading } = useApi<Project[]>(
    () => apiService.getProjects(),
    [],
  );

  const { execute: uploadDocument, loading: uploading } = useAsyncAction(
    (data: { projectId: string; file: File }) =>
      apiService.uploadDocument(data.projectId, data.file),
  );

  const { execute: addUrl, loading: addingUrl } = useAsyncAction(
    (data: { projectId: string; url: string }) =>
      apiService.addUrl(data.projectId, data.url),
  );

  // Mock documents data
  const mockDocuments: Document[] = [
    {
      id: "1",
      name: "Product FAQ.pdf",
      type: "pdf",
      size: 2048576,
      uploadedAt: new Date("2024-01-15"),
      status: "completed",
    },
    {
      id: "2",
      name: "User Manual.docx",
      type: "docx",
      size: 1536000,
      uploadedAt: new Date("2024-01-14"),
      status: "completed",
    },
    {
      id: "3",
      name: "API Documentation",
      type: "url",
      size: 0,
      uploadedAt: new Date("2024-01-13"),
      status: "processing",
      url: "https://api.example.com/docs",
    },
    {
      id: "4",
      name: "Troubleshooting Guide.txt",
      type: "txt",
      size: 512000,
      uploadedAt: new Date("2024-01-12"),
      status: "error",
    },
  ];

  const handleFileUpload = async () => {
    if (!selectedProject || !selectedFile) {
      toast({
        title: "Error",
        description: "Please select a project and file",
        variant: "destructive",
      });
      return;
    }

    try {
      await uploadDocument({ projectId: selectedProject, file: selectedFile });
      toast({
        title: "Success",
        description: "Document uploaded successfully",
      });
      setUploadDialogOpen(false);
      setSelectedFile(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload document",
        variant: "destructive",
      });
    }
  };

  const handleAddUrl = async () => {
    if (!selectedProject || !newUrl) {
      toast({
        title: "Error",
        description: "Please select a project and enter a URL",
        variant: "destructive",
      });
      return;
    }

    try {
      await addUrl({ projectId: selectedProject, url: newUrl });
      toast({
        title: "Success",
        description: "URL added successfully",
      });
      setUrlDialogOpen(false);
      setNewUrl("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add URL",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "processing":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-500">Completed</Badge>;
      case "processing":
        return <Badge className="bg-yellow-500">Processing</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case "url":
        return <Globe className="h-5 w-5 text-blue-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "N/A";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const filteredDocuments = mockDocuments.filter((doc) => {
    const matchesSearch = doc.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === "all" || doc.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Base</h1>
          <p className="text-muted-foreground">
            Upload documents and add URLs to enhance your AI responses with
            relevant knowledge.
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={urlDialogOpen} onOpenChange={setUrlDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Globe className="mr-2 h-4 w-4" />
                Add URL
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add URL to Knowledge Base</DialogTitle>
                <DialogDescription>
                  Add a website URL to scrape and include in your knowledge
                  base.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-select">Project</Label>
                  <Select
                    value={selectedProject}
                    onValueChange={setSelectedProject}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects?.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="url">URL</Label>
                  <Input
                    id="url"
                    type="url"
                    placeholder="https://example.com/docs"
                    value={newUrl}
                    onChange={(e) => setNewUrl(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={handleAddUrl}
                  disabled={addingUrl || !selectedProject || !newUrl}
                >
                  {addingUrl ? "Adding..." : "Add URL"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Upload Document
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Document</DialogTitle>
                <DialogDescription>
                  Upload a document to add to your knowledge base. Supported
                  formats: PDF, DOCX, TXT.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-select">Project</Label>
                  <Select
                    value={selectedProject}
                    onValueChange={setSelectedProject}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a project" />
                    </SelectTrigger>
                    <SelectContent>
                      {projects?.map((project) => (
                        <SelectItem key={project.id} value={project.id}>
                          {project.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="file">File</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".pdf,.docx,.txt"
                    onChange={(e) =>
                      setSelectedFile(e.target.files?.[0] || null)
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  onClick={handleFileUpload}
                  disabled={uploading || !selectedProject || !selectedFile}
                >
                  {uploading ? "Uploading..." : "Upload"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Project Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Project</CardTitle>
          <CardDescription>
            Choose which project's knowledge base you want to manage.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a project" />
            </SelectTrigger>
            <SelectContent>
              {projects?.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedProject && (
        <>
          {/* Knowledge Base Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Documents
                </CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockDocuments.length}</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Processing
                </CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {
                    mockDocuments.filter((d) => d.status === "processing")
                      .length
                  }
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently being indexed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {mockDocuments.filter((d) => d.status === "completed").length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Ready for AI queries
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Size
                </CardTitle>
                <Download className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatFileSize(
                    mockDocuments.reduce((sum, doc) => sum + doc.size, 0),
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all documents
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filter */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search documents..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Documents List */}
          <Card>
            <CardHeader>
              <CardTitle>Documents & URLs</CardTitle>
              <CardDescription>
                Manage your knowledge base content and monitor processing
                status.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredDocuments.map((document) => (
                  <div
                    key={document.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      {getFileIcon(document.type)}
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{document.name}</h4>
                          {getStatusBadge(document.status)}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>Size: {formatFileSize(document.size)}</span>
                          <span>
                            Uploaded: {document.uploadedAt.toLocaleDateString()}
                          </span>
                          {document.url && (
                            <span className="text-blue-500">
                              {document.url}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {document.status === "processing" && (
                        <div className="flex items-center space-x-2">
                          <Progress value={65} className="w-20" />
                          <span className="text-sm text-muted-foreground">
                            65%
                          </span>
                        </div>
                      )}
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {filteredDocuments.length === 0 && (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">
                    No documents found
                  </h3>
                  <p className="text-muted-foreground">
                    {searchQuery || filterStatus !== "all"
                      ? "Try adjusting your search or filter criteria."
                      : "Upload your first document to get started."}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {!selectedProject && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <FileText className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  Select a project to manage knowledge base
                </h3>
                <p className="text-muted-foreground">
                  Choose a project from the dropdown above to start managing its
                  knowledge base.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default KnowledgeBasePage;
