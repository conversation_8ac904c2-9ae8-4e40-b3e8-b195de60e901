import { Suspense } from "react";
import { useRoutes, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import Home from "@/components/home";
import AiProvidersPage from "@/components/pages/AiProvidersPage";
import WidgetCustomizationPage from "@/components/pages/WidgetCustomizationPage";
import KnowledgeBasePage from "@/components/pages/KnowledgeBasePage";
import ConversationsPage from "@/components/pages/ConversationsPage";
import AnalyticsPage from "@/components/pages/AnalyticsPage";
import SettingsPage from "@/components/pages/SettingsPage";
import LandingPage from "@/components/pages/LandingPage";
import Login from "@/components/auth/Login";
import Register from "@/components/auth/Register";
import routes from "tempo-routes";

function AppContent() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <p>Loading...</p>
        </div>
      }
    >
      <>
        <Routes>
          {/* Public Routes */}
          <Route path="/landing" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Dashboard Routes */}
          <Route
            path="/"
            element={
              <DashboardLayout>
                <Home />
              </DashboardLayout>
            }
          />
          <Route
            path="/ai-providers"
            element={
              <DashboardLayout>
                <AiProvidersPage />
              </DashboardLayout>
            }
          />
          <Route
            path="/widget-customization"
            element={
              <DashboardLayout>
                <WidgetCustomizationPage />
              </DashboardLayout>
            }
          />
          <Route
            path="/knowledge-base"
            element={
              <DashboardLayout>
                <KnowledgeBasePage />
              </DashboardLayout>
            }
          />
          <Route
            path="/conversations"
            element={
              <DashboardLayout>
                <ConversationsPage />
              </DashboardLayout>
            }
          />
          <Route
            path="/analytics"
            element={
              <DashboardLayout>
                <AnalyticsPage />
              </DashboardLayout>
            }
          />
          <Route
            path="/settings"
            element={
              <DashboardLayout>
                <SettingsPage />
              </DashboardLayout>
            }
          />

          {/* Redirect root to landing */}
          <Route path="*" element={<Navigate to="/landing" replace />} />
        </Routes>
        {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
        <Toaster />
      </>
    </Suspense>
  );
}

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
