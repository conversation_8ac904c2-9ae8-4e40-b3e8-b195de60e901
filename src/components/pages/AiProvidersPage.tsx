import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import AiProviderCard from "@/components/ai-providers/AiProviderCard";
import { useApi, useAsyncAction } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { AiProvider } from "@/types";
import { Plus, Loader2 } from "lucide-react";

const AiProvidersPage = () => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newProvider, setNewProvider] = useState({
    name: "",
    apiKey: "",
    priority: 1,
  });

  const {
    data: providers,
    loading,
    error,
    refetch,
  } = useApi<AiProvider[]>(() => apiService.getAiProviders(), []);

  const { execute: createProvider, loading: creating } = useAsyncAction(
    (data: Partial<AiProvider>) => apiService.createAiProvider(data),
  );

  const handleCreateProvider = async () => {
    try {
      await createProvider({
        ...newProvider,
        logo: `https://api.dicebear.com/7.x/initials/svg?seed=${newProvider.name}`,
        isActive: false,
        connectionStatus: "disconnected",
        models: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      toast({
        title: "Success",
        description: "AI provider created successfully",
      });

      setIsDialogOpen(false);
      setNewProvider({ name: "", apiKey: "", priority: 1 });
      refetch();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create AI provider",
        variant: "destructive",
      });
    }
  };

  const availableProviders = [
    { name: "OpenAI", models: ["GPT-4", "GPT-3.5 Turbo"] },
    { name: "Claude", models: ["Claude 3", "Claude 2"] },
    { name: "Gemini", models: ["Gemini Pro", "Gemini Ultra"] },
    { name: "Cohere", models: ["Command", "Command Light"] },
    { name: "Hugging Face", models: ["Various Models"] },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">AI Providers</h1>
            <p className="text-muted-foreground">
              Configure and manage your AI provider connections.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">AI Providers</h1>
            <p className="text-muted-foreground">
              Configure and manage your AI provider connections.
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Error loading AI providers: {error}
            </p>
            <div className="flex justify-center mt-4">
              <Button onClick={refetch}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Providers</h1>
          <p className="text-muted-foreground">
            Configure and manage your AI provider connections and API keys.
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Provider
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add AI Provider</DialogTitle>
              <DialogDescription>
                Configure a new AI provider for your chat widgets.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="provider-name">Provider</Label>
                <Select
                  value={newProvider.name}
                  onValueChange={(value) =>
                    setNewProvider({ ...newProvider, name: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a provider" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProviders.map((provider) => (
                      <SelectItem key={provider.name} value={provider.name}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="api-key">API Key</Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="Enter your API key"
                  value={newProvider.apiKey}
                  onChange={(e) =>
                    setNewProvider({ ...newProvider, apiKey: e.target.value })
                  }
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={newProvider.priority.toString()}
                  onValueChange={(value) =>
                    setNewProvider({
                      ...newProvider,
                      priority: parseInt(value),
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 (Highest)</SelectItem>
                    <SelectItem value="2">2</SelectItem>
                    <SelectItem value="3">3</SelectItem>
                    <SelectItem value="4">4</SelectItem>
                    <SelectItem value="5">5 (Lowest)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                onClick={handleCreateProvider}
                disabled={creating || !newProvider.name || !newProvider.apiKey}
              >
                {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add Provider
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Provider Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {providers?.map((provider) => (
          <AiProviderCard key={provider.id} provider={provider} />
        ))}
      </div>

      {/* Empty State */}
      {providers?.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <Plus className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  No AI providers configured
                </h3>
                <p className="text-muted-foreground">
                  Add your first AI provider to start building intelligent chat
                  widgets.
                </p>
              </div>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Your First Provider
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AiProvidersPage;
