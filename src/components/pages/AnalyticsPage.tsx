import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useApi } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { Project, Analytics } from "@/types";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  MessageSquare,
  Clock,
  Star,
  Download,
  Calendar,
  Activity,
  Zap,
  Target,
} from "lucide-react";

const AnalyticsPage = () => {
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [timeRange, setTimeRange] = useState<string>("7d");

  const { data: projects, loading: projectsLoading } = useApi<Project[]>(
    () => apiService.getProjects(),
    [],
  );

  const { data: analytics, loading: analyticsLoading } = useApi<Analytics>(
    () => apiService.getAnalytics(selectedProject),
    [selectedProject],
  );

  // Mock additional analytics data
  const mockMetrics = {
    responseTimeData: [
      { time: "00:00", value: 1.2 },
      { time: "04:00", value: 0.8 },
      { time: "08:00", value: 1.5 },
      { time: "12:00", value: 2.1 },
      { time: "16:00", value: 1.8 },
      { time: "20:00", value: 1.3 },
    ],
    satisfactionTrend: [
      { date: "Jan 1", value: 4.2 },
      { date: "Jan 8", value: 4.3 },
      { date: "Jan 15", value: 4.6 },
      { date: "Jan 22", value: 4.5 },
      { date: "Jan 29", value: 4.7 },
    ],
    topIssues: [
      { issue: "Password Reset", count: 145, trend: "up" },
      { issue: "Billing Questions", count: 132, trend: "down" },
      { issue: "Technical Support", count: 98, trend: "up" },
      { issue: "Account Setup", count: 76, trend: "stable" },
      { issue: "Feature Requests", count: 54, trend: "up" },
    ],
    aiPerformance: [
      {
        provider: "OpenAI",
        model: "GPT-4",
        usage: 65,
        avgResponseTime: 1.2,
        successRate: 94,
        cost: 45.67,
      },
      {
        provider: "Claude",
        model: "Claude 3",
        usage: 25,
        avgResponseTime: 1.8,
        successRate: 91,
        cost: 23.45,
      },
      {
        provider: "Gemini",
        model: "Gemini Pro",
        usage: 10,
        avgResponseTime: 2.1,
        successRate: 88,
        cost: 12.34,
      },
    ],
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  if (analyticsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
            <p className="text-muted-foreground">
              Monitor performance and gain insights into your AI support system.
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Monitor performance and gain insights into your AI support system.
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[120px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Project Selection */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Select
                value={selectedProject}
                onValueChange={setSelectedProject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Projects</SelectItem>
                  {projects?.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Conversations
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.totalConversations || 0}
            </div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +12% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.averageResponseTime || 0}s
            </div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingDown className="h-3 w-3 mr-1 text-green-500" />
              -0.3s improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              User Satisfaction
            </CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.userSatisfaction || 0}/5
            </div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              +0.2 from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Chats</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.activeConversations || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently in progress
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">AI Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Queries */}
            <Card>
              <CardHeader>
                <CardTitle>Top Queries</CardTitle>
                <CardDescription>
                  Most common questions from users
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics?.topQueries?.map((query, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <div className="flex-1">
                        <p className="font-medium">{query.query}</p>
                        <div className="flex items-center mt-1">
                          <div className="w-full bg-muted rounded-full h-2 mr-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{
                                width: `${(query.count / (analytics.topQueries[0]?.count || 1)) * 100}%`,
                              }}
                            ></div>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {query.count}
                          </span>
                        </div>
                      </div>
                    </div>
                  )) || []}
                </div>
              </CardContent>
            </Card>

            {/* Conversations by Hour */}
            <Card>
              <CardHeader>
                <CardTitle>Activity by Hour</CardTitle>
                <CardDescription>
                  Conversation volume throughout the day
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analytics?.conversationsByHour?.map((data, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="text-sm w-12">
                        {data.hour.toString().padStart(2, "0")}:00
                      </span>
                      <div className="flex-1">
                        <Progress
                          value={(data.count / 20) * 100}
                          className="h-2"
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-8">
                        {data.count}
                      </span>
                    </div>
                  )) || []}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Provider Usage */}
          <Card>
            <CardHeader>
              <CardTitle>AI Provider Usage</CardTitle>
              <CardDescription>
                Distribution of AI provider usage across conversations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {analytics?.providerUsage?.map((provider, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl font-bold">{provider.usage}%</div>
                    <p className="text-sm text-muted-foreground">
                      {provider.provider}
                    </p>
                    <Progress value={provider.usage} className="mt-2" />
                  </div>
                )) || []}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Model Performance</CardTitle>
              <CardDescription>
                Detailed performance metrics for each AI provider and model
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockMetrics.aiPerformance.map((ai, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div>
                        <h4 className="font-medium">{ai.provider}</h4>
                        <p className="text-sm text-muted-foreground">
                          {ai.model}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 gap-8 text-center">
                      <div>
                        <div className="text-lg font-bold">{ai.usage}%</div>
                        <p className="text-xs text-muted-foreground">Usage</p>
                      </div>
                      <div>
                        <div className="text-lg font-bold">
                          {ai.avgResponseTime}s
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Avg Response
                        </p>
                      </div>
                      <div>
                        <div className="text-lg font-bold">
                          {ai.successRate}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Success Rate
                        </p>
                      </div>
                      <div>
                        <div className="text-lg font-bold">
                          {formatCurrency(ai.cost)}
                        </div>
                        <p className="text-xs text-muted-foreground">Cost</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Response Time Trends</CardTitle>
                <CardDescription>
                  Average response time throughout the day
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {mockMetrics.responseTimeData.map((data, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <span className="text-sm w-12">{data.time}</span>
                      <div className="flex-1">
                        <Progress
                          value={(data.value / 3) * 100}
                          className="h-2"
                        />
                      </div>
                      <span className="text-sm text-muted-foreground w-12">
                        {data.value}s
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cost Analysis</CardTitle>
                <CardDescription>
                  AI usage costs and optimization opportunities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Monthly Cost</span>
                    <span className="font-bold text-lg">
                      {formatCurrency(
                        mockMetrics.aiPerformance.reduce(
                          (sum, ai) => sum + ai.cost,
                          0,
                        ),
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Cost per Conversation</span>
                    <span className="font-medium">
                      {formatCurrency(
                        mockMetrics.aiPerformance.reduce(
                          (sum, ai) => sum + ai.cost,
                          0,
                        ) / (analytics?.totalConversations || 1),
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Projected Monthly</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(
                        (mockMetrics.aiPerformance.reduce(
                          (sum, ai) => sum + ai.cost,
                          0,
                        ) *
                          30) /
                          7,
                      )}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Issues</CardTitle>
                <CardDescription>
                  Most common support issues and their trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockMetrics.topIssues.map((issue, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getTrendIcon(issue.trend)}
                        <div>
                          <p className="font-medium">{issue.issue}</p>
                          <p className="text-sm text-muted-foreground">
                            {issue.count} occurrences
                          </p>
                        </div>
                      </div>
                      <Badge
                        variant={
                          issue.trend === "up"
                            ? "destructive"
                            : issue.trend === "down"
                              ? "default"
                              : "secondary"
                        }
                      >
                        {issue.trend}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
                <CardDescription>
                  AI-powered insights to improve your support system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                    <Target className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-blue-900">
                        Optimize Response Times
                      </p>
                      <p className="text-sm text-blue-700">
                        Consider switching to faster models during peak hours
                        (12-16:00) to improve response times.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                    <Zap className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-green-900">
                        Knowledge Base Enhancement
                      </p>
                      <p className="text-sm text-green-700">
                        Add more content about "Password Reset" to reduce
                        escalations by an estimated 23%.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <p className="font-medium text-yellow-900">
                        Cost Optimization
                      </p>
                      <p className="text-sm text-yellow-700">
                        Switch 30% of simple queries to a more cost-effective
                        model to save ~$15/month.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsPage;
