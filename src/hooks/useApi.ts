import { useState, useEffect } from "react";
import { ApiResponse } from "@/types";

export function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  dependencies: any[] = [],
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiCall();

      if (response.success) {
        setData(response.data);
      } else {
        setError(response.message || "An error occurred");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

export function useAsyncAction<T, P = void>(
  action: (params: P) => Promise<ApiResponse<T>>,
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = async (params: P) => {
    try {
      setLoading(true);
      setError(null);
      const response = await action(params);

      if (response.success) {
        setData(response.data);
        return response.data;
      } else {
        setError(response.message || "An error occurred");
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    execute,
    loading,
    error,
    data,
    reset: () => {
      setError(null);
      setData(null);
    },
  };
}
