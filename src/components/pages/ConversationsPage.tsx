import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useApi } from "@/hooks/useApi";
import { apiService } from "@/services/api";
import { Project, Conversation, Message } from "@/types";
import {
  Search,
  Filter,
  MessageSquare,
  Clock,
  User,
  Bot,
  Star,
  Download,
  Eye,
  MoreHorizontal,
  Calendar,
  Tag,
} from "lucide-react";

const ConversationsPage = () => {
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedConversation, setSelectedConversation] =
    useState<Conversation | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const { data: projects, loading: projectsLoading } = useApi<Project[]>(
    () => apiService.getProjects(),
    [],
  );

  // Mock conversations data
  const mockConversations: Conversation[] = [
    {
      id: "1",
      projectId: "1",
      userId: "user_123",
      status: "closed",
      startedAt: new Date("2024-01-15T10:30:00"),
      endedAt: new Date("2024-01-15T10:45:00"),
      satisfaction: 5,
      tags: ["billing", "resolved"],
      messages: [
        {
          id: "1",
          content: "Hi, I have a question about my billing.",
          sender: "user",
          timestamp: new Date("2024-01-15T10:30:00"),
        },
        {
          id: "2",
          content:
            "Hello! I'd be happy to help you with your billing question. What specific issue are you experiencing?",
          sender: "bot",
          timestamp: new Date("2024-01-15T10:30:15"),
          metadata: {
            aiProvider: "OpenAI",
            model: "GPT-4",
            tokens: 45,
            responseTime: 1.2,
          },
        },
        {
          id: "3",
          content:
            "I was charged twice for my subscription this month. Can you help me understand why?",
          sender: "user",
          timestamp: new Date("2024-01-15T10:31:00"),
        },
        {
          id: "4",
          content:
            "I understand your concern about the double charge. Let me check your account details. It appears there was a billing system error that caused a duplicate charge. I'll initiate a refund for the extra charge immediately. You should see the refund in 3-5 business days.",
          sender: "bot",
          timestamp: new Date("2024-01-15T10:31:30"),
          metadata: {
            aiProvider: "OpenAI",
            model: "GPT-4",
            tokens: 78,
            responseTime: 2.1,
          },
        },
        {
          id: "5",
          content: "Thank you so much! That resolves my issue.",
          sender: "user",
          timestamp: new Date("2024-01-15T10:45:00"),
        },
      ],
    },
    {
      id: "2",
      projectId: "1",
      userId: "user_456",
      status: "active",
      startedAt: new Date("2024-01-15T14:20:00"),
      satisfaction: undefined,
      tags: ["technical", "ongoing"],
      messages: [
        {
          id: "6",
          content: "I'm having trouble connecting to the API.",
          sender: "user",
          timestamp: new Date("2024-01-15T14:20:00"),
        },
        {
          id: "7",
          content:
            "I'm sorry to hear you're having trouble with the API connection. Let me help you troubleshoot this issue. Can you tell me what error message you're seeing?",
          sender: "bot",
          timestamp: new Date("2024-01-15T14:20:15"),
          metadata: {
            aiProvider: "Claude",
            model: "Claude 3",
            tokens: 52,
            responseTime: 1.8,
          },
        },
      ],
    },
    {
      id: "3",
      projectId: "1",
      userId: "user_789",
      status: "transferred",
      startedAt: new Date("2024-01-14T16:45:00"),
      endedAt: new Date("2024-01-14T17:30:00"),
      satisfaction: 4,
      tags: ["complex", "escalated"],
      messages: [
        {
          id: "8",
          content:
            "I need help with a complex integration issue that requires custom development.",
          sender: "user",
          timestamp: new Date("2024-01-14T16:45:00"),
        },
        {
          id: "9",
          content:
            "This sounds like a complex technical issue that would benefit from our specialized support team. Let me transfer you to a human agent who can provide more detailed assistance.",
          sender: "bot",
          timestamp: new Date("2024-01-14T16:45:30"),
          metadata: {
            aiProvider: "OpenAI",
            model: "GPT-4",
            tokens: 38,
            responseTime: 1.5,
          },
        },
        {
          id: "10",
          content:
            "Hello! I'm Sarah from our technical support team. I understand you need help with a custom integration. Let me review your requirements...",
          sender: "agent",
          timestamp: new Date("2024-01-14T16:50:00"),
        },
      ],
    },
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "closed":
        return <Badge variant="outline">Closed</Badge>;
      case "transferred":
        return <Badge className="bg-blue-500">Transferred</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSatisfactionStars = (rating?: number) => {
    if (!rating) return null;
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-muted-foreground">({rating}/5)</span>
      </div>
    );
  };

  const formatDuration = (start: Date, end?: Date) => {
    if (!end) return "Ongoing";
    const duration = Math.round((end.getTime() - start.getTime()) / 60000);
    return `${duration} min`;
  };

  const filteredConversations = mockConversations.filter((conv) => {
    const matchesSearch =
      conv.messages.some((msg) =>
        msg.content.toLowerCase().includes(searchQuery.toLowerCase()),
      ) ||
      conv.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    const matchesStatus =
      statusFilter === "all" || conv.status === statusFilter;
    const matchesProject =
      !selectedProject || conv.projectId === selectedProject;
    return matchesSearch && matchesStatus && matchesProject;
  });

  const handleViewConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setDialogOpen(true);
  };

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Conversations</h1>
          <p className="text-muted-foreground">
            Monitor and manage all customer conversations across your projects.
          </p>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Export Data
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Conversations
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockConversations.length}</div>
            <p className="text-xs text-muted-foreground">+12% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Chats</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockConversations.filter((c) => c.status === "active").length}
            </div>
            <p className="text-xs text-muted-foreground">Currently ongoing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Satisfaction
            </CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(
                mockConversations
                  .filter((c) => c.satisfaction)
                  .reduce((sum, c) => sum + (c.satisfaction || 0), 0) /
                mockConversations.filter((c) => c.satisfaction).length
              ).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">Out of 5 stars</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Resolution Rate
            </CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">
              Issues resolved by AI
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={selectedProject} onValueChange={setSelectedProject}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="All Projects" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Projects</SelectItem>
                {projects?.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
                <SelectItem value="transferred">Transferred</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Conversations List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Conversations</CardTitle>
          <CardDescription>
            View and manage customer conversations with detailed insights.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center space-x-4 flex-1">
                  <Avatar>
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${conversation.userId}`}
                    />
                    <AvatarFallback>
                      {conversation.userId.slice(-2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium">
                        User {conversation.userId.slice(-3)}
                      </span>
                      {getStatusBadge(conversation.status)}
                      <span className="text-sm text-muted-foreground">
                        {formatDuration(
                          conversation.startedAt,
                          conversation.endedAt,
                        )}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-1">
                      {conversation.messages[0]?.content}
                    </p>
                    <div className="flex items-center space-x-4 mt-2">
                      <div className="flex items-center space-x-1">
                        <MessageSquare className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {conversation.messages.length} messages
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">
                          {conversation.startedAt.toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {conversation.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getSatisfactionStars(conversation.satisfaction)}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleViewConversation(conversation)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {filteredConversations.length === 0 && (
            <div className="text-center py-8">
              <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">
                No conversations found
              </h3>
              <p className="text-muted-foreground">
                {searchQuery || statusFilter !== "all" || selectedProject
                  ? "Try adjusting your search or filter criteria."
                  : "Conversations will appear here once users start chatting."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Conversation Detail Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <span>Conversation Details</span>
              {selectedConversation &&
                getStatusBadge(selectedConversation.status)}
            </DialogTitle>
            <DialogDescription>
              {selectedConversation && (
                <div className="flex items-center space-x-4 text-sm">
                  <span>
                    User: {selectedConversation.userId.slice(-3).toUpperCase()}
                  </span>
                  <span>
                    Started: {selectedConversation.startedAt.toLocaleString()}
                  </span>
                  <span>
                    Duration:{" "}
                    {formatDuration(
                      selectedConversation.startedAt,
                      selectedConversation.endedAt,
                    )}
                  </span>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto">
            {selectedConversation && (
              <div className="space-y-4">
                {selectedConversation.messages.map((message, index) => (
                  <div key={message.id}>
                    <div
                      className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg p-3 ${
                          message.sender === "user"
                            ? "bg-blue-500 text-white"
                            : message.sender === "bot"
                              ? "bg-gray-100 text-gray-800"
                              : "bg-green-100 text-green-800"
                        }`}
                      >
                        <div className="flex items-center space-x-2 mb-1">
                          {message.sender === "user" ? (
                            <User className="h-4 w-4" />
                          ) : message.sender === "bot" ? (
                            <Bot className="h-4 w-4" />
                          ) : (
                            <User className="h-4 w-4" />
                          )}
                          <span className="text-xs font-medium">
                            {message.sender === "user"
                              ? "User"
                              : message.sender === "bot"
                                ? "AI Assistant"
                                : "Agent"}
                          </span>
                          <span className="text-xs opacity-70">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm">{message.content}</p>
                        {message.metadata && (
                          <div className="mt-2 text-xs opacity-70">
                            <div className="flex items-center space-x-2">
                              <span>{message.metadata.aiProvider}</span>
                              <span>•</span>
                              <span>{message.metadata.model}</span>
                              <span>•</span>
                              <span>{message.metadata.tokens} tokens</span>
                              <span>•</span>
                              <span>{message.metadata.responseTime}s</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    {index < selectedConversation.messages.length - 1 && (
                      <Separator className="my-4" />
                    )}
                  </div>
                ))}
                {selectedConversation.satisfaction && (
                  <div className="mt-6 p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Customer Satisfaction</h4>
                    {getSatisfactionStars(selectedConversation.satisfaction)}
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ConversationsPage;
