import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  Card<PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  <PERSON>H<PERSON>zon<PERSON>,
  Edit,
  Eye,
  Trash2,
  MessageSquare,
  Clock,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ProjectCardProps {
  id?: string;
  name?: string;
  description?: string;
  status?: "active" | "inactive" | "maintenance";
  metrics?: {
    activeChats?: number;
    uptime?: number;
    responseRate?: number;
  };
  onEdit?: (id: string) => void;
  onView?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const ProjectCard = ({
  id = "1",
  name = "Customer Support Bot",
  description = "AI-powered customer support chatbot with knowledge base integration",
  status = "active",
  metrics = {
    activeChats: 12,
    uptime: 99.8,
    responseRate: 92,
  },
  onEdit = () => {},
  onView = () => {},
  onDelete = () => {},
}: ProjectCardProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "inactive":
        return "bg-gray-400";
      case "maintenance":
        return "bg-amber-500";
      default:
        return "bg-gray-400";
    }
  };

  const handleEdit = () => onEdit(id);
  const handleView = () => onView(id);
  const handleDelete = () => onDelete(id);

  return (
    <Card className="w-full max-w-md bg-white shadow-md hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <div
              className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}
            ></div>
            <Badge
              variant={
                status === "active"
                  ? "default"
                  : status === "maintenance"
                    ? "secondary"
                    : "outline"
              }
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleView}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Project
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Project
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="text-xl font-bold">{name}</CardTitle>
        <CardDescription className="line-clamp-2">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4 py-2">
          <div className="flex flex-col items-center">
            <div className="flex items-center text-muted-foreground mb-1">
              <MessageSquare className="h-4 w-4 mr-1" />
              <span className="text-xs">Active Chats</span>
            </div>
            <p className="font-semibold text-lg">{metrics.activeChats}</p>
          </div>
          <div className="flex flex-col items-center">
            <div className="flex items-center text-muted-foreground mb-1">
              <Clock className="h-4 w-4 mr-1" />
              <span className="text-xs">Uptime</span>
            </div>
            <p className="font-semibold text-lg">{metrics.uptime}%</p>
          </div>
          <div className="flex flex-col items-center">
            <div className="flex items-center text-muted-foreground mb-1">
              <BarChart className="h-4 w-4 mr-1" />
              <span className="text-xs">Response</span>
            </div>
            <p className="font-semibold text-lg">{metrics.responseRate}%</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <Button variant="outline" size="sm" onClick={handleEdit}>
          <Edit className="h-4 w-4 mr-2" />
          Edit
        </Button>
        <Button size="sm" onClick={handleView}>
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProjectCard;
