import {
  AiProvider,
  Project,
  Conversation,
  Analytics,
  ApiResponse,
  PaginatedResponse,
  WidgetConfig,
  KnowledgeBase,
} from "@/types";

// Base API configuration
const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:8000/api";
const API_TOKEN = localStorage.getItem("auth_token");

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        ...(API_TOKEN && { Authorization: `Bearer ${API_TOKEN}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "API request failed");
      }

      return data;
    } catch (error) {
      // Simulate API responses for development
      return this.simulateApiResponse<T>(endpoint, options);
    }
  }

  private simulateApiResponse<T>(
    endpoint: string,
    options: RequestInit,
  ): ApiResponse<T> {
    // Simulate different responses based on endpoint
    const delay = Math.random() * 1000 + 500; // 500-1500ms delay

      return new Promise<ApiResponse<T>>((resolve) => {
      setTimeout(() => {
        if (endpoint.includes("/ai-providers")) {
          resolve(this.getMockAiProviders() as ApiResponse<T>);
        } else if (endpoint.includes("/projects")) {
          resolve(this.getMockProjects() as ApiResponse<T>);
        } else if (endpoint.includes("/conversations")) {
          resolve(this.getMockConversations() as ApiResponse<T>);
        } else if (endpoint.includes("/analytics")) {
          resolve(this.getMockAnalytics() as ApiResponse<T>);
        } else {
          resolve({
            data: {} as T,
            message: "Success",
            success: true,
          });
        }
      }, delay);
    }) as Promise<ApiResponse<T>>;
  }

  // AI Providers
  async getAiProviders(): Promise<ApiResponse<AiProvider[]>> {
    return this.request<AiProvider[]>("/ai-providers");
  }

  async createAiProvider(
    provider: Partial<AiProvider>,
  ): Promise<ApiResponse<AiProvider>> {
    return this.request<AiProvider>("/ai-providers", {
      method: "POST",
      body: JSON.stringify(provider),
    });
  }

  async updateAiProvider(
    id: string,
    provider: Partial<AiProvider>,
  ): Promise<ApiResponse<AiProvider>> {
    return this.request<AiProvider>(`/ai-providers/${id}`, {
      method: "PUT",
      body: JSON.stringify(provider),
    });
  }

  async deleteAiProvider(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/ai-providers/${id}`, {
      method: "DELETE",
    });
  }

  async testAiProviderConnection(
    id: string,
  ): Promise<ApiResponse<{ status: string }>> {
    return this.request<{ status: string }>(`/ai-providers/${id}/test`);
  }

  // Projects
  async getProjects(): Promise<ApiResponse<Project[]>> {
    return this.request<Project[]>("/projects");
  }

  async createProject(
    project: Partial<Project>,
  ): Promise<ApiResponse<Project>> {
    return this.request<Project>("/projects", {
      method: "POST",
      body: JSON.stringify(project),
    });
  }

  async updateProject(
    id: string,
    project: Partial<Project>,
  ): Promise<ApiResponse<Project>> {
    return this.request<Project>(`/projects/${id}`, {
      method: "PUT",
      body: JSON.stringify(project),
    });
  }

  async deleteProject(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${id}`, {
      method: "DELETE",
    });
  }

  // Widget Configuration
  async updateWidgetConfig(
    projectId: string,
    config: Partial<WidgetConfig>,
  ): Promise<ApiResponse<WidgetConfig>> {
    return this.request<WidgetConfig>(`/projects/${projectId}/widget`, {
      method: "PUT",
      body: JSON.stringify(config),
    });
  }

  // Knowledge Base
  async uploadDocument(
    projectId: string,
    file: File,
  ): Promise<ApiResponse<Document>> {
    const formData = new FormData();
    formData.append("document", file);

    return this.request<Document>(
      `/projects/${projectId}/knowledge-base/documents`,
      {
        method: "POST",
        body: formData,
        headers: {}, // Remove Content-Type to let browser set it for FormData
      },
    );
  }

  async addUrl(projectId: string, url: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${projectId}/knowledge-base/urls`, {
      method: "POST",
      body: JSON.stringify({ url }),
    });
  }

  // Conversations
  async getConversations(
    projectId?: string,
  ): Promise<ApiResponse<PaginatedResponse<Conversation>>> {
    const endpoint = projectId
      ? `/conversations?project_id=${projectId}`
      : "/conversations";
    return this.request<PaginatedResponse<Conversation>>(endpoint);
  }

  // Analytics
  async getAnalytics(projectId?: string): Promise<ApiResponse<Analytics>> {
    const endpoint = projectId
      ? `/analytics?project_id=${projectId}`
      : "/analytics";
    return this.request<Analytics>(endpoint);
  }

  // User Profile
  async updateProfile(profile: Partial<any>): Promise<ApiResponse<any>> {
    return this.request<any>("/profile", {
      method: "PUT",
      body: JSON.stringify(profile),
    });
  }

  // Notifications
  async updateNotifications(settings: any): Promise<ApiResponse<any>> {
    return this.request<any>("/notifications", {
      method: "PUT",
      body: JSON.stringify(settings),
    });
  }

  // Security
  async updateSecurity(settings: any): Promise<ApiResponse<any>> {
    return this.request<any>("/security", {
      method: "PUT",
      body: JSON.stringify(settings),
    });
  }

  // Team Management
  async inviteTeamMember(data: {
    email: string;
    role: string;
  }): Promise<ApiResponse<any>> {
    return this.request<any>("/team/invite", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async getTeamMembers(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>("/team");
  }

  async updateTeamMember(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request<any>(`/team/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  async removeTeamMember(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/team/${id}`, {
      method: "DELETE",
    });
  }

  // Mock data generators
  private getMockAiProviders(): ApiResponse<AiProvider[]> {
    const providers: AiProvider[] = [
      {
        id: "1",
        name: "OpenAI",
        logo: "https://api.dicebear.com/7.x/initials/svg?seed=OpenAI",
        isActive: true,
        apiKey: "",
        connectionStatus: "disconnected",
        priority: 1,
        models: [
          {
            id: "gpt-4",
            name: "GPT-4",
            contextWindow: 8192,
            maxTokens: 4096,
            costPer1kTokens: 0.03,
          },
          {
            id: "gpt-3.5-turbo",
            name: "GPT-3.5 Turbo",
            contextWindow: 4096,
            maxTokens: 2048,
            costPer1kTokens: 0.002,
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "2",
        name: "Claude",
        logo: "https://api.dicebear.com/7.x/initials/svg?seed=Claude",
        isActive: false,
        apiKey: "",
        connectionStatus: "disconnected",
        priority: 2,
        models: [
          {
            id: "claude-3",
            name: "Claude 3",
            contextWindow: 100000,
            maxTokens: 4096,
            costPer1kTokens: 0.015,
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: "3",
        name: "Gemini",
        logo: "https://api.dicebear.com/7.x/initials/svg?seed=Gemini",
        isActive: false,
        apiKey: "",
        connectionStatus: "disconnected",
        priority: 3,
        models: [
          {
            id: "gemini-pro",
            name: "Gemini Pro",
            contextWindow: 32768,
            maxTokens: 2048,
            costPer1kTokens: 0.001,
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: providers,
      message: "AI providers retrieved successfully",
      success: true,
    };
  }

  private getMockProjects(): ApiResponse<Project[]> {
    const projects: Project[] = [
      {
        id: "1",
        name: "Customer Support Bot",
        description:
          "AI-powered customer support chatbot with knowledge base integration",
        status: "active",
        metrics: {
          activeChats: 12,
          uptime: 99.8,
          responseRate: 92,
          averageResponseTime: 1.2,
          totalConversations: 1547,
          userSatisfaction: 4.6,
        },
        widgetConfig: {
          id: "1",
          primaryColor: "#F472B6",
          secondaryColor: "#ffffff",
          headerText: "Chat Support",
          welcomeMessage: "Hello! How can I help you today?",
          botName: "AI Assistant",
          botAvatarUrl:
            "https://api.dicebear.com/7.x/avataaars/svg?seed=assistant",
          companyLogoUrl: "https://api.dicebear.com/7.x/initials/svg?seed=CS",
          position: "bottom-right",
          width: 350,
          height: 500,
          isEnabled: true,
          allowFileUpload: true,
          showTypingIndicator: true,
          enableSoundNotifications: false,
        },
        knowledgeBase: {
          id: "1",
          documents: [],
          urls: [],
          lastUpdated: new Date(),
          totalDocuments: 0,
          isProcessing: false,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: projects,
      message: "Projects retrieved successfully",
      success: true,
    };
  }

  private getMockConversations(): ApiResponse<PaginatedResponse<Conversation>> {
    return {
      data: {
        data: [],
        pagination: {
          current_page: 1,
          per_page: 10,
          total: 0,
          last_page: 1,
        },
      },
      message: "Conversations retrieved successfully",
      success: true,
    };
  }

  private getMockAnalytics(): ApiResponse<Analytics> {
    return {
      data: {
        totalConversations: 1547,
        activeConversations: 12,
        averageResponseTime: 1.2,
        userSatisfaction: 4.6,
        topQueries: [
          { query: "How to reset password?", count: 45 },
          { query: "Billing questions", count: 32 },
          { query: "Technical support", count: 28 },
        ],
        conversationsByHour: Array.from({ length: 24 }, (_, i) => ({
          hour: i,
          count: Math.floor(Math.random() * 20),
        })),
        providerUsage: [
          { provider: "OpenAI", usage: 65 },
          { provider: "Claude", usage: 25 },
          { provider: "Gemini", usage: 10 },
        ],
      },
      message: "Analytics retrieved successfully",
      success: true,
    };
  }
}

export const apiService = new ApiService();
