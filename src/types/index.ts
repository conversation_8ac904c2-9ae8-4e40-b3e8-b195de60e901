export interface AiProvider {
  id: string;
  name: string;
  logo: string;
  isActive: boolean;
  apiKey: string;
  connectionStatus: "connected" | "disconnected" | "error" | "testing";
  models: AiModel[];
  priority: number;
  maxTokens?: number;
  temperature?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AiModel {
  id: string;
  name: string;
  contextWindow: number;
  maxTokens: number;
  costPer1kTokens: number;
  description?: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive" | "maintenance";
  metrics: ProjectMetrics;
  widgetConfig: WidgetConfig;
  knowledgeBase: KnowledgeBase;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectMetrics {
  activeChats: number;
  uptime: number;
  responseRate: number;
  averageResponseTime: number;
  totalConversations: number;
  userSatisfaction: number;
}

export interface WidgetConfig {
  id: string;
  primaryColor: string;
  secondaryColor: string;
  headerText: string;
  welcomeMessage: string;
  botName: string;
  botAvatarUrl: string;
  companyLogoUrl: string;
  position: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  width: number;
  height: number;
  isEnabled: boolean;
  customCss?: string;
  allowFileUpload: boolean;
  showTypingIndicator: boolean;
  enableSoundNotifications: boolean;
}

export interface KnowledgeBase {
  id: string;
  documents: Document[];
  urls: string[];
  lastUpdated: Date;
  totalDocuments: number;
  isProcessing: boolean;
}

export interface Document {
  id: string;
  name: string;
  type: "pdf" | "docx" | "txt" | "url";
  size: number;
  uploadedAt: Date;
  status: "processing" | "completed" | "error";
  url?: string;
}

export interface Conversation {
  id: string;
  projectId: string;
  userId: string;
  messages: Message[];
  status: "active" | "closed" | "transferred";
  startedAt: Date;
  endedAt?: Date;
  satisfaction?: number;
  tags: string[];
}

export interface Message {
  id: string;
  content: string;
  sender: "user" | "bot" | "agent";
  timestamp: Date;
  metadata?: {
    aiProvider?: string;
    model?: string;
    tokens?: number;
    responseTime?: number;
  };
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: "admin" | "agent" | "viewer";
  avatar?: string;
  createdAt: Date;
  lastLoginAt?: Date;
}

export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
  };
}

export interface Analytics {
  totalConversations: number;
  activeConversations: number;
  averageResponseTime: number;
  userSatisfaction: number;
  topQueries: Array<{ query: string; count: number }>;
  conversationsByHour: Array<{ hour: number; count: number }>;
  providerUsage: Array<{ provider: string; usage: number }>;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  phone?: string;
  location?: string;
  company?: string;
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  weeklyReports: boolean;
  securityAlerts: boolean;
  marketingEmails: boolean;
}

export interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  ipWhitelist: string[];
  apiKeyRotation: boolean;
  lastPasswordChange?: Date;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: "admin" | "agent" | "viewer";
  avatar?: string;
  status: "active" | "inactive" | "pending";
  lastLogin?: Date;
  invitedAt?: Date;
  invitedBy?: string;
}

export interface ApiKey {
  id: string;
  name: string;
  key: string;
  environment: "production" | "development" | "staging";
  permissions: string[];
  lastUsed?: Date;
  createdAt: Date;
  expiresAt?: Date;
}

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  metadata?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
}
