import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronDown,
  ChevronUp,
  Check,
  AlertCircle,
  Loader2,
} from "lucide-react";

interface AiModel {
  id: string;
  name: string;
  contextWindow: number;
  maxTokens: number;
}

interface AiProviderCardProps {
  provider?: {
    id: string;
    name: string;
    logo: string;
    isActive: boolean;
    apiKey: string;
    connectionStatus: "connected" | "disconnected" | "error" | "testing";
    models: AiModel[];
  };
}

const AiProviderCard = ({
  provider = {
    id: "1",
    name: "OpenAI",
    logo: "https://api.dicebear.com/7.x/avataaars/svg?seed=openai",
    isActive: true,
    apiKey: "",
    connectionStatus: "disconnected",
    models: [
      { id: "gpt-4", name: "GPT-4", contextWindow: 8192, maxTokens: 4096 },
      {
        id: "gpt-3.5-turbo",
        name: "GPT-3.5 Turbo",
        contextWindow: 4096,
        maxTokens: 2048,
      },
    ],
  },
}: AiProviderCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isActive, setIsActive] = useState(provider.isActive);
  const [apiKey, setApiKey] = useState(provider.apiKey);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "disconnected" | "error" | "testing"
  >(provider.connectionStatus);
  const [selectedModel, setSelectedModel] = useState<string | undefined>(
    provider.models[0]?.id,
  );

  const handleTestConnection = () => {
    setConnectionStatus("testing");

    // Simulate API call
    setTimeout(() => {
      if (apiKey.length > 10) {
        setConnectionStatus("connected");
      } else {
        setConnectionStatus("error");
      }
    }, 1500);
  };

  const getStatusBadge = () => {
    switch (connectionStatus) {
      case "connected":
        return (
          <Badge className="bg-green-500">
            Connected <Check className="ml-1 h-3 w-3" />
          </Badge>
        );
      case "error":
        return (
          <Badge variant="destructive">
            Error <AlertCircle className="ml-1 h-3 w-3" />
          </Badge>
        );
      case "testing":
        return (
          <Badge className="bg-yellow-500">
            Testing <Loader2 className="ml-1 h-3 w-3 animate-spin" />
          </Badge>
        );
      default:
        return <Badge variant="outline">Disconnected</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-md bg-white shadow-md">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div className="flex items-center space-x-4">
          <img
            src={provider.logo}
            alt={`${provider.name} logo`}
            className="h-10 w-10 rounded-md"
          />
          <div>
            <CardTitle className="text-xl">{provider.name}</CardTitle>
            <CardDescription>AI Provider</CardDescription>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusBadge()}
          <Switch
            checked={isActive}
            onCheckedChange={setIsActive}
            aria-label="Toggle provider active state"
          />
        </div>
      </CardHeader>

      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="apiKey" className="text-sm font-medium">
              API Key
            </label>
            <div className="flex space-x-2">
              <Input
                id="apiKey"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key"
                className="flex-1"
              />
              <Button
                onClick={handleTestConnection}
                variant="outline"
                disabled={connectionStatus === "testing" || !apiKey}
              >
                {connectionStatus === "testing" ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing
                  </>
                ) : (
                  "Test"
                )}
              </Button>
            </div>
          </div>

          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="flex w-full justify-between p-0"
              >
                <span>Available Models</span>
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="model" className="text-sm font-medium">
                  Select Model
                </label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    {provider.models.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        {model.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedModel && (
                <div className="rounded-md bg-muted p-4">
                  <h4 className="mb-2 font-medium">Model Capabilities</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">Context Window:</p>
                      <p>
                        {provider.models
                          .find((m) => m.id === selectedModel)
                          ?.contextWindow.toLocaleString()}{" "}
                        tokens
                      </p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Max Output:</p>
                      <p>
                        {provider.models
                          .find((m) => m.id === selectedModel)
                          ?.maxTokens.toLocaleString()}{" "}
                        tokens
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>
      </CardContent>

      <CardFooter className="flex justify-end pt-4">
        <Button>Save Changes</Button>
      </CardFooter>
    </Card>
  );
};

export default AiProviderCard;
